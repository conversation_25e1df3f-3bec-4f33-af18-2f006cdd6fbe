//! 闪电贷回调处理器
//!
//! 处理闪电贷执行过程中的回调逻辑，执行套利策略

use anchor_lang::prelude::*;
use crate::error::RouteError;
use crate::routing::types::{Route, RoutingMode};
use crate::routing::circular::CircularRouteExecutor;
use super::traits::{FlashLoanCallback, FlashLoanContext};

/// 闪电贷回调处理器
pub struct FlashLoanCallbackHandler {
    /// 最大执行步骤数
    max_steps: u8,
    /// 最大滑点（基点）
    max_slippage_bps: u16,
    /// 是否启用详细日志
    verbose_logging: bool,
}

impl Default for FlashLoanCallbackHandler {
    fn default() -> Self {
        Self {
            max_steps: 6,
            max_slippage_bps: 300, // 3%
            verbose_logging: true,
        }
    }
}

impl FlashLoanCallbackHandler {
    /// 创建新的回调处理器
    pub fn new(max_steps: u8, max_slippage_bps: u16, verbose_logging: bool) -> Self {
        Self {
            max_steps,
            max_slippage_bps,
            verbose_logging,
        }
    }

    /// 解析回调数据
    fn parse_callback_data(&self, data: &[u8]) -> Result<CallbackData> {
        if data.is_empty() {
            return Err(RouteError::InvalidCallbackData.into());
        }

        // 尝试反序列化回调数据
        CallbackData::try_from_slice(data)
            .map_err(|_| RouteError::InvalidCallbackData.into())
    }

    /// 验证套利路由的有效性
    fn validate_arbitrage_routes(&self, routes: &[Route]) -> Result<()> {
        if routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        if routes.len() > self.max_steps as usize {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证循环路由（套利必须是循环的）
        let start_mint = routes.first().unwrap().input_mint;
        let end_mint = routes.last().unwrap().output_mint;
        if start_mint != end_mint {
            return Err(RouteError::NotCircularRoute.into());
        }

        // 验证路由连续性
        for i in 0..routes.len() - 1 {
            if routes[i].output_mint != routes[i + 1].input_mint {
                return Err(RouteError::RouteDiscontinuity.into());
            }
        }

        // 验证每个路由步骤
        for route in routes {
            route.validate()?;
        }

        Ok(())
    }

    /// 执行套利路由
    fn execute_arbitrage_routes<'info, 'a>(
        &self,
        routes: &[Route],
        amount: u64,
        accounts: &'a [AccountInfo<'info>],
        _context: &FlashLoanContext,
    ) -> Result<u64> {
        if self.verbose_logging {
            msg!("开始执行套利路由 - 步骤数: {}, 金额: {}", routes.len(), amount);
        }

        // 使用循环路由执行器执行套利
        let result = CircularRouteExecutor::execute(
            routes,
            amount,
            accounts,
            None, // 闪电贷已经在外层处理
            None, // 不使用PDA
        )?;

        if self.verbose_logging {
            msg!("套利路由执行完成 - 输出金额: {}", result);
        }

        Ok(result)
    }

    /// 计算净利润
    fn calculate_net_profit(
        &self,
        gross_output: u64,
        flash_loan_amount: u64,
        flash_loan_fee: u64,
        gas_cost: u64,
    ) -> Result<u64> {
        let total_cost = flash_loan_amount
            .checked_add(flash_loan_fee)
            .and_then(|v| v.checked_add(gas_cost))
            .ok_or(RouteError::ArithmeticOverflow)?;

        if gross_output > total_cost {
            Ok(gross_output - total_cost)
        } else {
            // 亏损情况
            Ok(0)
        }
    }

    /// 验证最小利润要求
    fn validate_minimum_profit(
        &self,
        net_profit: u64,
        expected_profit: u64,
        tolerance_bps: u16,
    ) -> Result<()> {
        // 计算容忍范围
        let min_acceptable_profit = expected_profit
            .saturating_mul((10000 - tolerance_bps) as u64)
            .saturating_div(10000);

        if net_profit < min_acceptable_profit {
            msg!(
                "利润不符合预期 - 实际: {}, 预期: {}, 最小可接受: {}",
                net_profit, expected_profit, min_acceptable_profit
            );
            return Err(RouteError::InsufficientProfit.into());
        }

        Ok(())
    }

    /// 处理执行失败的情况
    fn handle_execution_failure(
        &self,
        error: &anchor_lang::error::Error,
        context: &FlashLoanContext,
    ) -> Result<u64> {
        msg!("套利执行失败 - 错误: {:?}, 提供者: {}", error, context.provider);

        // 记录失败统计
        // 这里可以添加失败分析和恢复逻辑

        // 在闪电贷场景中，失败意味着无法还款，这是严重错误
        Err(RouteError::ArbitrageExecutionFailed.into())
    }

    /// 估算Gas成本
    fn estimate_gas_cost(&self, routes: &[Route]) -> u64 {
        // 基础Gas成本
        let base_cost = 10_000u64;

        // 每个路由步骤的平均成本
        let per_step_cost = 15_000u64;

        // 闪电贷相关的额外成本
        let flash_loan_overhead = 5_000u64;

        base_cost + (routes.len() as u64 * per_step_cost) + flash_loan_overhead
    }
}

impl FlashLoanCallback for FlashLoanCallbackHandler {
    fn execute_callback<'info>(
        &self,
        context: &FlashLoanContext,
        accounts: &[AccountInfo<'info>],
        callback_data: &[u8]) -> Result<u64> {
        if self.verbose_logging {
            msg!(
                "开始执行闪电贷回调 - 提供者: {}, 金额: {}, 费用: {}",
                context.provider, context.amount, context.fee
            );
        }

        // 1. 检查超时
        if context.is_expired()? {
            return Err(RouteError::CallbackTimeout.into());
        }

        // 2. 解析回调数据
        let callback = self.parse_callback_data(callback_data)?;

        // 3. 验证回调数据
        callback.validate()?;

        // 4. 验证套利路由
        self.validate_arbitrage_routes(&callback.routes)?;

        // 5. 估算Gas成本
        let estimated_gas_cost = self.estimate_gas_cost(&callback.routes);

        // 6. 执行套利路由
        let start_time = Clock::get()?.unix_timestamp;

        let gross_output = match self.execute_arbitrage_routes(
            &callback.routes,
            context.amount,
            accounts,
            context,
        ) {
            Ok(output) => output,
            Err(e) => return self.handle_execution_failure(&e, context),
        };

        let execution_time = Clock::get()?.unix_timestamp - start_time;

        // 7. 计算净利润
        let net_profit = self.calculate_net_profit(
            gross_output,
            context.amount,
            context.fee,
            estimated_gas_cost,
        )?;

        // 8. 验证最小利润
        self.validate_minimum_profit(
            net_profit,
            callback.expected_profit,
            callback.profit_tolerance_bps,
        )?;

        if self.verbose_logging {
            msg!(
                "闪电贷回调执行完成 - 毛利: {}, 净利: {}, 执行时间: {}s",
                gross_output, net_profit, execution_time
            );
        }

        // 9. 发出执行完成事件
        emit!(FlashLoanCallbackExecutedEvent {
            provider: context.provider.to_string(),
            flash_loan_amount: context.amount,
            fee_paid: context.fee,
            gross_output,
            net_profit,
            execution_time_seconds: execution_time,
            steps_executed: callback.routes.len() as u8,
            timestamp: Clock::get()?.unix_timestamp,
        });

        Ok(net_profit)
    }


    fn validate_callback_data(&self, data: &[u8]) -> Result<()> {
        // 基本验证
        if data.is_empty() {
            return Err(RouteError::InvalidCallbackData.into());
        }

        if data.len() > 4096 { // 限制回调数据大小
            return Err(RouteError::CallbackDataTooLarge.into());
        }

        // 尝试解析数据以验证格式
        let callback = self.parse_callback_data(data)?;
        callback.validate()?;

        Ok(())
    }
}

/// 闪电贷回调数据结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct CallbackData {
    /// 套利路由
    pub routes: Vec<Route>,
    /// 预期利润
    pub expected_profit: u64,
    /// 利润容忍度（基点）
    pub profit_tolerance_bps: u16,
    /// 最大滑点（基点）
    pub max_slippage_bps: u16,
    /// 超时时间戳
    pub timeout_timestamp: i64,
    /// 执行优先级（1-10，10最高）
    pub priority: u8,
}

impl CallbackData {
    /// 创建新的回调数据
    pub fn new(
        routes: Vec<Route>,
        expected_profit: u64,
        profit_tolerance_bps: u16,
        max_slippage_bps: u16,
        timeout_seconds: i64,
        priority: u8,
    ) -> Result<Self> {
        let current_time = Clock::get()?.unix_timestamp;
        let timeout_timestamp = current_time + timeout_seconds;

        Ok(Self {
            routes,
            expected_profit,
            profit_tolerance_bps,
            max_slippage_bps,
            timeout_timestamp,
            priority: priority.min(10),
        })
    }

    /// 验证回调数据的有效性
    pub fn validate(&self) -> Result<()> {
        // 验证路由不为空
        if self.routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        // 验证路由数量限制
        if self.routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 验证预期利润
        if self.expected_profit == 0 {
            return Err(RouteError::InvalidExpectedProfit.into());
        }

        // 验证滑点设置
        if self.max_slippage_bps > 1000 { // 最大10%滑点
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 验证容忍度设置
        if self.profit_tolerance_bps > 5000 { // 最大50%容忍度
            return Err(RouteError::ProfitToleranceTooHigh.into());
        }

        // 验证是否已超时
        let current_time = Clock::get()?.unix_timestamp;
        if current_time > self.timeout_timestamp {
            return Err(RouteError::CallbackTimeout.into());
        }

        // 验证优先级
        if self.priority == 0 || self.priority > 10 {
            return Err(RouteError::InvalidPriority.into());
        }

        Ok(())
    }

    /// 检查是否即将超时（距离超时时间小于30秒）
    pub fn is_near_timeout(&self) -> Result<bool> {
        let current_time = Clock::get()?.unix_timestamp;
        let time_remaining = self.timeout_timestamp - current_time;
        Ok(time_remaining < 30)
    }

    /// 获取剩余时间（秒）
    pub fn time_remaining(&self) -> Result<i64> {
        let current_time = Clock::get()?.unix_timestamp;
        Ok((self.timeout_timestamp - current_time).max(0))
    }
}

/// 闪电贷回调执行完成事件
#[event]
pub struct FlashLoanCallbackExecutedEvent {
    /// 提供者名称
    pub provider: String,
    /// 闪电贷金额
    pub flash_loan_amount: u64,
    /// 支付的费用
    pub fee_paid: u64,
    /// 毛利润
    pub gross_output: u64,
    /// 净利润
    pub net_profit: u64,
    /// 执行时间（秒）
    pub execution_time_seconds: i64,
    /// 执行的步骤数
    pub steps_executed: u8,
    /// 时间戳
    pub timestamp: i64,
}

/// 高级回调处理器
/// 支持更复杂的回调逻辑，如条件执行、重试机制等
pub struct AdvancedCallbackHandler {
    /// 基础处理器
    base_handler: FlashLoanCallbackHandler,
    /// 最大重试次数
    max_retries: u8,
    /// 重试延迟（秒）
    retry_delay_seconds: u64,
    /// 是否启用条件执行
    conditional_execution: bool,
}

impl AdvancedCallbackHandler {
    pub fn new(
        base_handler: FlashLoanCallbackHandler,
        max_retries: u8,
        retry_delay_seconds: u64,
        conditional_execution: bool,
    ) -> Self {
        Self {
            base_handler,
            max_retries,
            retry_delay_seconds,
            conditional_execution,
        }
    }

    /// 带重试的回调执行
    pub fn execute_with_retry<'info>(
        &self,
        context: &FlashLoanContext,
        accounts: &'info [AccountInfo<'info>],
        callback_data: &[u8],
    ) -> Result<u64> {
        let mut last_error: Option<anchor_lang::error::Error> = None;

        for attempt in 0..=self.max_retries {
            match self.base_handler.execute_callback(context, accounts, callback_data) {
                Ok(result) => {
                    if attempt > 0 {
                        msg!("重试成功 - 尝试次数: {}", attempt + 1);
                    }
                    return Ok(result);
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < self.max_retries {
                        msg!("执行失败，准备重试 - 尝试: {}/{}", attempt + 1, self.max_retries + 1);
                        // 在实际实现中，这里可能需要等待机制
                        // 但在Solana程序中通常不支持延迟
                    }
                }
            }
        }

        msg!("所有重试都失败了");
        match last_error {
            Some(e) => Err(e),
            None => Err(RouteError::UnknownError.into()),
        }
    }

    /// 条件执行检查
    pub fn should_execute(&self, callback: &CallbackData) -> Result<bool> {
        if !self.conditional_execution {
            return Ok(true);
        }

        // 检查优先级
        if callback.priority < 5 {
            // 低优先级任务可能需要额外条件
            return Ok(false);
        }

        // 检查是否接近超时
        if callback.is_near_timeout()? {
            return Ok(false);
        }

        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_callback_handler_creation() {
        let handler = FlashLoanCallbackHandler::default();
        assert_eq!(handler.max_steps, 6);
        assert_eq!(handler.max_slippage_bps, 300);
        assert_eq!(handler.verbose_logging, true);
    }

    #[test]
    fn test_callback_data_validation() {
        // 这个测试需要在实际Solana环境中运行，因为使用了Clock::get()
        // 在单元测试中可以使用mock或者修改代码结构来支持测试
    }
}
