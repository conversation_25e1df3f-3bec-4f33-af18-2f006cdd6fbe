//! 统一的日志记录和错误处理工具
//! 
//! 提供结构化的日志记录、错误转换和监控能力

use anchor_lang::prelude::*;
use crate::error::{RouteError, RouteErrorContext, ErrorStatistics};
use crate::ErrorRecoveryAction;
use crate::routing::types::{Dex, RoutingMode};

/// 操作日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LogLevel {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warn = 3,
    Error = 4,
    Fatal = 5,
}

/// 操作类型分类
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum OperationType {
    RouteExecution,
    DexOperation,
    FlashLoan,
    SecurityCheck,
    AccountValidation,
    MathCalculation,
    SystemConfig,
}

/// 日志上下文信息
#[derive(Debug, Clone)]
pub struct LogContext {
    pub operation_type: OperationType,
    pub user: Option<Pubkey>,
    pub order_id: Option<u64>,
    pub dex: Option<Dex>,
    pub step: Option<u8>,
    pub amount: Option<u64>,
    pub additional_data: Option<String>,
}

impl LogContext {
    pub fn new(operation_type: OperationType) -> Self {
        Self {
            operation_type,
            user: None,
            order_id: None,
            dex: None,
            step: None,
            amount: None,
            additional_data: None,
        }
    }

    pub fn with_user(mut self, user: Pubkey) -> Self {
        self.user = Some(user);
        self
    }

    pub fn with_order_id(mut self, order_id: u64) -> Self {
        self.order_id = Some(order_id);
        self
    }

    pub fn with_dex(mut self, dex: Dex) -> Self {
        self.dex = Some(dex);
        self
    }

    pub fn with_step(mut self, step: u8) -> Self {
        self.step = Some(step);
        self
    }

    pub fn with_amount(mut self, amount: u64) -> Self {
        self.amount = Some(amount);
        self
    }

    pub fn with_data(mut self, data: impl Into<String>) -> Self {
        self.additional_data = Some(data.into());
        self
    }
}

/// 套利操作日志记录
pub fn log_arbitrage_operation(
    level: LogLevel,
    message: &str,
    context: &LogContext,
) {
    let timestamp = Clock::get().map(|c| c.unix_timestamp).unwrap_or(0);
    let user_str = context.user.map(|u| u.to_string()).unwrap_or_else(|| "Unknown".to_string());
    let order_str = context.order_id.map(|id| id.to_string()).unwrap_or_else(|| "None".to_string());
    let dex_str = context.dex.as_ref().map(|d| format!("{:?}", d)).unwrap_or_else(|| "None".to_string());
    let step_str = context.step.map(|s| s.to_string()).unwrap_or_else(|| "None".to_string());
    let amount_str = context.amount.map(|a| a.to_string()).unwrap_or_else(|| "None".to_string());

    msg!(
        "[{}][{:?}] {} | User: {} | Order: {} | DEX: {} | Step: {} | Amount: {} | Data: {} | Time: {}",
        level as u8,
        context.operation_type,
        message,
        user_str,
        order_str,
        dex_str,
        step_str,
        amount_str,
        context.additional_data.as_ref().unwrap_or(&"None".to_string()),
        timestamp
    );
}

/// 路由执行错误日志记录
pub fn log_route_execution_error(
    error: &RouteError,
    context: &str,
    user: Option<&Pubkey>,
    order_id: Option<u64>,
    failed_step: Option<u8>,
    failed_dex: Option<&Dex>,
) {
    let log_context = LogContext::new(OperationType::RouteExecution)
        .with_data(context);

    let mut log_context = if let Some(user) = user {
        log_context.with_user(*user)
    } else {
        log_context
    };

    if let Some(order_id) = order_id {
        log_context = log_context.with_order_id(order_id);
    }

    if let Some(step) = failed_step {
        log_context = log_context.with_step(step);
    }

    if let Some(dex) = failed_dex {
        log_context = log_context.with_dex(*dex);
    }

    let error_msg = format!("RouteError[{}]: {}", *error as u32, error);
    log_arbitrage_operation(LogLevel::Error, &error_msg, &log_context);
}

/// DEX操作日志记录
pub fn log_dex_operation(
    dex: &Dex,
    operation: &str,
    amount_in: u64,
    amount_out: u64,
    success: bool,
    user: Option<&Pubkey>,
    order_id: Option<u64>,
) {
    let level = if success { LogLevel::Info } else { LogLevel::Error };
    let status = if success { "SUCCESS" } else { "FAILED" };
    
    let mut context = LogContext::new(OperationType::DexOperation)
        .with_dex(*dex)
        .with_amount(amount_in)
        .with_data(format!("Op: {} | Out: {} | Status: {}", operation, amount_out, status));

    if let Some(user) = user {
        context = context.with_user(*user);
    }

    if let Some(order_id) = order_id {
        context = context.with_order_id(order_id);
    }

    let message = format!("DEX operation: {}", operation);
    log_arbitrage_operation(level, &message, &context);
}

/// 闪电贷操作日志记录
pub fn log_flash_loan_operation(
    provider: &Pubkey,
    amount: u64,
    fee: u64,
    success: bool,
    user: Option<&Pubkey>,
    order_id: Option<u64>,
    details: Option<&str>,
) {
    let level = if success { LogLevel::Info } else { LogLevel::Error };
    let status = if success { "SUCCESS" } else { "FAILED" };
    
    let mut context = LogContext::new(OperationType::FlashLoan)
        .with_amount(amount)
        .with_data(format!(
            "Provider: {} | Fee: {} | Status: {} | Details: {}", 
            provider, 
            fee, 
            status,
            details.unwrap_or("None")
        ));

    if let Some(user) = user {
        context = context.with_user(*user);
    }

    if let Some(order_id) = order_id {
        context = context.with_order_id(order_id);
    }

    let message = "Flash loan operation";
    log_arbitrage_operation(level, message, &context);
}

/// 安全检查日志记录
pub fn log_security_check(
    check_type: &str,
    success: bool,
    user: Option<&Pubkey>,
    details: Option<&str>,
) {
    let level = if success { LogLevel::Debug } else { LogLevel::Warn };
    let status = if success { "PASSED" } else { "FAILED" };
    
    let mut context = LogContext::new(OperationType::SecurityCheck)
        .with_data(format!(
            "Type: {} | Status: {} | Details: {}", 
            check_type, 
            status,
            details.unwrap_or("None")
        ));

    if let Some(user) = user {
        context = context.with_user(*user);
    }

    let message = format!("Security check: {}", check_type);
    log_arbitrage_operation(level, &message, &context);
}

/// 账户验证日志记录
pub fn log_account_validation(
    account_type: &str,
    account: &Pubkey,
    success: bool,
    user: Option<&Pubkey>,
    error_details: Option<&str>,
) {
    let level = if success { LogLevel::Debug } else { LogLevel::Error };
    let status = if success { "VALID" } else { "INVALID" };
    
    let mut context = LogContext::new(OperationType::AccountValidation)
        .with_data(format!(
            "Type: {} | Account: {} | Status: {} | Error: {}", 
            account_type, 
            account, 
            status,
            error_details.unwrap_or("None")
        ));

    if let Some(user) = user {
        context = context.with_user(*user);
    }

    let message = format!("Account validation: {}", account_type);
    log_arbitrage_operation(level, &message, &context);
}

/// 数学计算错误日志记录
pub fn log_math_error(
    calculation_type: &str,
    input_values: &[u64],
    error: &RouteError,
    user: Option<&Pubkey>,
) {
    let context = LogContext::new(OperationType::MathCalculation)
        .with_data(format!(
            "Type: {} | Inputs: {:?} | Error: {}", 
            calculation_type, 
            input_values,
            error
        ));

    let context = if let Some(user) = user {
        context.with_user(*user)
    } else {
        context
    };

    let message = format!("Math calculation error: {}", calculation_type);
    log_arbitrage_operation(LogLevel::Error, &message, &context);
}

/// 系统配置变更日志记录
pub fn log_config_change(
    admin: &Pubkey,
    config_field: &str,
    old_value: &str,
    new_value: &str,
) {
    let context = LogContext::new(OperationType::SystemConfig)
        .with_user(*admin)
        .with_data(format!(
            "Field: {} | Old: {} | New: {}", 
            config_field, 
            old_value, 
            new_value
        ));

    let message = "System configuration changed";
    log_arbitrage_operation(LogLevel::Info, &message, &context);
}

/// 性能指标日志记录
pub fn log_performance_metrics(
    operation: &str,
    execution_time_ms: u64,
    gas_used: u64,
    user: Option<&Pubkey>,
    order_id: Option<u64>,
) {
    let mut context = LogContext::new(OperationType::RouteExecution)
        .with_data(format!(
            "Operation: {} | Time: {}ms | Gas: {}", 
            operation, 
            execution_time_ms, 
            gas_used
        ));

    if let Some(user) = user {
        context = context.with_user(*user);
    }

    if let Some(order_id) = order_id {
        context = context.with_order_id(order_id);
    }

    let message = "Performance metrics";
    log_arbitrage_operation(LogLevel::Info, &message, &context);
}

/// 批量错误统计日志记录
pub fn log_error_statistics(stats: &ErrorStatistics, period: &str) {
    let context = LogContext::new(OperationType::SystemConfig)
        .with_data(format!(
            "Period: {} | Total: {} | Retryable: {} | Fatal: {} | Rate: {:.2}%", 
            period,
            stats.total_errors,
            stats.retryable_errors,
            stats.fatal_errors,
            stats.fatal_error_rate() * 100.0
        ));

    let message = "Error statistics summary";
    log_arbitrage_operation(LogLevel::Info, &message, &context);
}

/// 创建带完整上下文的错误
pub fn create_error_with_full_context(
    error: RouteError,
    operation: &str,
    user: Option<Pubkey>,
    order_id: Option<u64>,
    additional_context: Option<&str>,
) -> RouteErrorContext {
    let full_context = if let Some(additional) = additional_context {
        format!("{} | {}", operation, additional)
    } else {
        operation.to_string()
    };

    RouteErrorContext::new(error, full_context, user, order_id)
}

/// 错误恢复建议
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RecoveryAction {
    Retry,
    RetryWithDelay(u64),  // 延迟毫秒数
    UseAlternativeRoute,
    ReduceAmount(u8),     // 减少百分比
    SkipStep,
    AbortRoute,
    SwitchDex,
}

/// 获取错误的恢复建议
pub fn get_recovery_suggestion(error: &RouteError, attempt_count: u8) -> Option<RecoveryAction> {
    if attempt_count >= 3 {
        return Some(RecoveryAction::AbortRoute);
    }

    match error {
        RouteError::InsufficientLiquidity => {
            if attempt_count == 0 {
                Some(RecoveryAction::SwitchDex)
            } else {
                Some(RecoveryAction::ReduceAmount(10))
            }
        },
        RouteError::SlippageTooHigh => {
            Some(RecoveryAction::RetryWithDelay(1000))
        },
        RouteError::DexCpiCallFailed => {
            Some(RecoveryAction::Retry)
        },
        RouteError::OperationTimeout => {
            Some(RecoveryAction::RetryWithDelay(500))
        },
        RouteError::PriceImpactTooHigh => {
            Some(RecoveryAction::ReduceAmount(20))
        },
        RouteError::RoutePathTooLong => {
            Some(RecoveryAction::UseAlternativeRoute)
        },
        RouteError::GlobalEmergencyStop
        | RouteError::UserSuspended
        | RouteError::ReentrancyDetected
        | RouteError::PermissionDenied => {
            None  // 无法恢复的致命错误
        },
        _ => {
            if attempt_count == 0 {
                Some(RecoveryAction::Retry)
            } else {
                Some(RecoveryAction::AbortRoute)
            }
        }
    }
}

/// 执行错误恢复日志记录
pub fn log_recovery_attempt(
    error: &RouteError,
    action: &ErrorRecoveryAction,
    attempt_count: u8,
    user: Option<&Pubkey>,
    order_id: Option<u64>,
) {
    let mut context = LogContext::new(OperationType::RouteExecution)
        .with_data(format!(
            "Error: {} | Action: {:?} | Attempt: {}", 
            error, 
            action, 
            attempt_count
        ));

    if let Some(user) = user {
        context = context.with_user(*user);
    }

    if let Some(order_id) = order_id {
        context = context.with_order_id(order_id);
    }

    let message = "Error recovery attempt";
    log_arbitrage_operation(LogLevel::Warn, &message, &context);
}

/// 格式化日志消息的宏
#[macro_export]
macro_rules! log_route {
    ($level:expr, $op_type:expr, $msg:expr) => {
        crate::utils::logging::log_arbitrage_operation(
            $level,
            $msg,
            &crate::utils::logging::LogContext::new($op_type),
        );
    };
    ($level:expr, $op_type:expr, $msg:expr, $($key:ident = $value:expr),+) => {
        {
            let mut context = crate::utils::logging::LogContext::new($op_type);
            $(
                context = context.$key($value);
            )+
            crate::utils::logging::log_arbitrage_operation($level, $msg, &context);
        }
    };
}

/// 便捷的错误日志记录宏
#[macro_export]
macro_rules! log_error {
    ($error:expr, $context:expr) => {
        crate::utils::logging::log_route_execution_error(
            &$error,
            $context,
            None,
            None,
            None,
            None,
        );
    };
    ($error:expr, $context:expr, user = $user:expr) => {
        crate::utils::logging::log_route_execution_error(
            &$error,
            $context,
            Some(&$user),
            None,
            None,
            None,
        );
    };
    ($error:expr, $context:expr, user = $user:expr, order = $order:expr) => {
        crate::utils::logging::log_route_execution_error(
            &$error,
            $context,
            Some(&$user),
            Some($order),
            None,
            None,
        );
    };
}