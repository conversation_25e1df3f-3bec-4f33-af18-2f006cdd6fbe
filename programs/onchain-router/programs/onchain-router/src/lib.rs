use anchor_lang::prelude::*;

// 程序ID设置为文档中指定的ID
declare_id!("11111111111111111111111111111112");

// 导入所有模块
pub mod adapters;
pub mod processor;
pub mod instructions;
pub mod state;
pub mod utils;
pub mod routing;
pub mod error;
pub mod constants;
pub mod flash_loan;
pub mod arbitrage;

// 重新导出错误类型
pub use error::*;

#[program]
pub mod onchain_router {
    use super::*;

    /// 初始化路由器配置
    /// 设置全局配置参数，如支持的DEX列表、费率等
    pub fn initialize_config(
        ctx: Context<instructions::InitializeConfig>,
        config_data: instructions::ConfigArgs,
    ) -> Result<()> {
        instructions::initialize_config_handler(ctx, config_data)
    }

    /// 初始化用户位置
    pub fn initialize_user_position(
        ctx: Context<instructions::InitializeUserPosition>,
    ) -> Result<()> {
        instructions::initialize_user_position_handler(ctx)
    }

    /// 执行通用路由
    /// 支持线性路由、循环路由（套利）、分支路由等多种模式
    pub fn execute_route<'a>(
        ctx: Context<'_, '_, 'a, 'a, instructions::ExecuteRouteAccounts<'a>>,
        route_config: routing::types::RouteConfig,
        order_id: u64,
    ) -> Result<()> {
        instructions::execute_route_handler(ctx, route_config, order_id)
    }
}
