use anchor_lang::prelude::*;

// 程序ID设置为文档中指定的ID
declare_id!("11111111111111111111111111111112");

// 导入所有模块
pub mod adapters;
pub mod processor;
pub mod instructions;
pub mod state;
pub mod utils;
pub mod routing;
pub mod error;
pub mod constants;
pub mod flash_loan;
pub mod arbitrage;

// 重新导出错误类型
pub use error::*;

#[program]
pub mod onchain_router {
    use super::*;

    /// 初始化路由器配置
    /// 设置全局配置参数，如支持的DEX列表、费率等
    pub fn initialize_config(
        ctx: Context<instructions::InitializeConfig>,
        config_data: instructions::ConfigArgs,
    ) -> Result<()> {
        instructions::initialize_config_handler(ctx, config_data)
    }
}
