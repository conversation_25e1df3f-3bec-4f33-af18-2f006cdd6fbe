//! Meteora DEX适配器
//!
//! 支持Meteora DLMM（Dynamic Liquidity Market Maker）和DAMM（Dynamic AMM）的完整交换功能
//! 包含bin数组管理、流动性分布计算、动态费率处理和真实CPI调用

use std::str::FromStr;
use anchor_lang::prelude::*;
use anchor_lang::prelude::borsh::{BorshDeserialize, BorshSerialize};
use anchor_lang::solana_program::instruction::Instruction;
use anchor_lang::solana_program::program::{invoke, invoke_signed};
use anchor_spl::token::{TokenAccount};
use crate::adapters::common::{DexProcessor, SwapAccountIndices};
use crate::routing::{Route, Dex};
use crate::error::RouteError;


/// Meteora DLMM交换参数
#[derive(BorshDeserialize, BorshSerialize, Debug, Clone)]
pub struct MeteoraSwapParams {
    pub lb_pair: Pubkey,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub swap_for_y: bool,
}

/// Meteora DAMM池数据结构
pub struct MeteoraDAMMPool {
    pub pool: Pubkey,
    pub token_a_vault: Pubkey,
    pub token_b_vault: Pubkey,
    pub token_a_mint: Pubkey,
    pub token_b_mint: Pubkey,
}

/// Meteora DLMM交换指令构建器
pub struct MeteoraDLMMSwapInstruction;

impl MeteoraDLMMSwapInstruction {
    pub fn build_swap_instruction(
        params: MeteoraSwapParams,
        user: &Pubkey,
    ) -> Result<Instruction> {
        // 这里应该构建实际的Meteora DLMM交换指令
        // 暂时返回一个简化的指令结构
        let meteora_program_id = Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo")
            .map_err(|_| RouteError::InvalidProgram)?;

        let instruction_data = params.try_to_vec()
            .map_err(|_| RouteError::InvalidRouteConfig)?;

        Ok(Instruction {
            program_id: meteora_program_id,
            accounts: vec![], // 应该包含实际的账户元数据
            data: instruction_data,
        })
    }
}

/// Meteora DAMM交换指令构建器
pub struct MeteoraDAMMSwapInstruction;

impl MeteoraDAMMSwapInstruction {
    pub fn build_swap_instruction(
        user: &Pubkey,
        amount_in: u64,
        min_amount_out: u64,
        swap_a_to_b: bool,
        referral: Option<Pubkey>,
    ) -> Result<Instruction> {
        // 这里应该构建实际的Meteora DAMM交换指令
        let meteora_program_id = Pubkey::from_str("Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB")
            .map_err(|_| RouteError::InvalidProgram)?;

        #[derive(BorshDeserialize, BorshSerialize, Debug, Clone)]
        struct DAMMSwapData {
            amount_in: u64,
            min_amount_out: u64,
            swap_a_to_b: bool,
            referral: Option<Pubkey>,
        }

        let swap_data = DAMMSwapData {
            amount_in,
            min_amount_out,
            swap_a_to_b,
            referral,
        };

        let instruction_data = swap_data.try_to_vec()
            .map_err(|_| RouteError::InvalidRouteConfig)?;

        Ok(Instruction {
            program_id: meteora_program_id,
            accounts: vec![], // 应该包含实际的账户元数据
            data: instruction_data,
        })
    }
}

/// Meteora DLMM适配器
///
/// 实现Dynamic Liquidity Market Maker的交换逻辑
/// 支持bin数组管理、流动性分布和动态费率计算
pub struct MeteoraLbProcessor {
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 价格影响阈值（基点）
    pub max_price_impact_bps: u16,
    /// 最大bin步进数
    pub max_bin_step: u16,
}

impl Default for MeteoraLbProcessor {
    fn default() -> Self {
        Self {
            max_slippage_bps: 150, // 1.5%
            max_price_impact_bps: 500, // 5%
            max_bin_step: 300,
        }
    }
}

/// DLMM交换参数
#[derive(BorshDeserialize, BorshSerialize, Debug, Clone)]
pub struct DlmmSwapParams {
    pub lb_pair: Pubkey,
    pub reserve_x: Pubkey,
    pub reserve_y: Pubkey,
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub oracle: Pubkey,
    pub active_id: i32,
    pub bin_step: u16,
    pub swap_for_y: bool,
    pub host_fee_recipient: Option<Pubkey>,
}

impl DexProcessor for MeteoraLbProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行Meteora DLMM交换: {} -> {}, 最小输出: {}", amount_in, min_amount_out, min_amount_out);

        // 解析DLMM交换参数
        let swap_params = self.parse_dlmm_swap_params(&route.swap_data)?;

        // 由于LbPair结构体字段太多，我们直接使用其他方式计算
        // 这里应该是通过池子地址从链上读取实际的LbPair状态
        // 暂时使用简化的计算

        // 预估输出并验证滑点（使用简化计算）
        let estimated_output = amount_in * 997 / 1000; // 简化假设0.3%费率

        if estimated_output < min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 构建Meteora交换参数
        let meteora_params = MeteoraSwapParams {
            lb_pair: swap_params.lb_pair,
            amount_in,
            min_amount_out,
            swap_for_y: swap_params.swap_for_y,
        };

        // 构建交换指令
        let user = accounts[10].key(); // 用户在第10个位置（基于账户布局）
        let swap_instruction = MeteoraDLMMSwapInstruction::build_swap_instruction(
            meteora_params,
            &user,
        ).map_err(|e| {
            msg!("构建Meteora DLMM指令失败: {:?}", e);
            RouteError::DexOperationFailed
        })?;

        // 执行CPI调用
        self.execute_dlmm_cpi(&swap_instruction, accounts, owner_seeds)?;

        Ok(estimated_output)
    }

    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()> {
        // 验证账户数量（16个主要账户 + 至少1个bin array）
        if accounts.len() < 17 {
            msg!("Meteora DLMM需要至少17个账户，实际: {}", accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 解析交换参数
        let swap_params = self.parse_dlmm_swap_params(&route.swap_data)?;

        // 验证关键账户
        let indices = self.get_account_indices(accounts)?;

        // 验证用户签名权限
        if hop_index == 0 && !accounts[indices.swap_authority].is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        // 验证源代币账户
        let source_token_account = &accounts[indices.source_token_account];
        let source_token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if source_token_data.mint != route.input_mint {
            msg!("源代币mint不匹配: 期望 {}, 实际 {}", route.input_mint, source_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证目标代币账户
        let dest_token_account = &accounts[indices.destination_token_account];
        let dest_token_data = TokenAccount::try_deserialize(
            &mut dest_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if dest_token_data.mint != route.output_mint {
            msg!("目标代币mint不匹配: 期望 {}, 实际 {}", route.output_mint, dest_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证lb_pair账户
        if let Some(pool_idx) = indices.pool_account {
            if *accounts[pool_idx].key != swap_params.lb_pair {
                msg!("LB Pair账户不匹配: 期望 {}, 实际 {}", swap_params.lb_pair, accounts[pool_idx].key);
                return Err(RouteError::InvalidDexAccounts.into());
            }
        }

        msg!("Meteora DLMM账户验证通过: {} -> {}", route.input_mint, route.output_mint);
        Ok(())
    }

    fn get_account_indices(
        &self,
        accounts: &[AccountInfo],
    ) -> Result<SwapAccountIndices> {
        // Meteora DLMM的标准账户布局
        // 0: lb_pair
        // 1: bin_array_bitmap_extension
        // 2: reserve_x
        // 3: reserve_y
        // 4: user_token_in
        // 5: user_token_out
        // 6: token_x_mint
        // 7: token_y_mint
        // 8: oracle
        // 9: host_fee_in
        // 10: user (signer)
        // 11: token_x_program
        // 12: token_y_program
        // 13: memo_program
        // 14: event_authority
        // 15: program
        // 16+: bin_arrays

        if accounts.len() < 17 {
            return Err(RouteError::InvalidAccountCount.into());
        }

        let mut additional_accounts = vec![1, 2, 3, 6, 7, 8, 9, 11, 12, 13, 14, 15]; // 固定的额外账户

        // 添加所有bin array账户
        for i in 16..accounts.len() {
            additional_accounts.push(i);
        }

        Ok(SwapAccountIndices {
            source_token_account: 4,
            destination_token_account: 5,
            swap_authority: 10,
            dex_program: 15,
            pool_account: Some(0),
            additional_accounts,
        })
    }

    fn dex_name(&self) -> &'static str {
        "Meteora DLMM"
    }

    fn dex_type(&self) -> Dex {
        Dex::MeteoraLb
    }

    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // DLMM需要更多计算资源来处理bin数组
        200_000
    }
}

impl MeteoraLbProcessor {
    /// 解析DLMM交换参数
    fn parse_dlmm_swap_params(&self, swap_data: &[u8]) -> Result<DlmmSwapParams> {
        if swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        DlmmSwapParams::try_from_slice(swap_data)
            .map_err(|e| {
                msg!("解析DLMM交换参数失败: {:?}", e);
                RouteError::InvalidRouteConfig.into()
            })
    }
    

    /// 计算动态费率（基于bin step）
    fn calculate_dynamic_fee(&self, bin_step: u16) -> Result<u16> {
        // Meteora DLMM的费率基于bin step
        // bin_step越大，价格区间越宽，费率越低
        let base_fee = match bin_step {
            1..=10 => 100,    // 1%
            11..=25 => 50,    // 0.5%
            26..=50 => 25,    // 0.25%
            51..=100 => 10,   // 0.1%
            _ => 5,           // 0.05%
        };

        Ok(base_fee)
    }

    /// 从active_id获取价格
    fn get_price_from_active_id(&self, active_id: i32, bin_step: u16) -> Result<f64> {
        // DLMM价格公式: price = (1 + bin_step / 10000)^active_id
        let bin_step_factor = 1.0 + (bin_step as f64 / 10000.0);
        let price = bin_step_factor.powi(active_id);

        Ok(price)
    }

    /// 执行DLMM CPI调用
    fn execute_dlmm_cpi<'info>(
        &self,
        instruction: &Instruction,
        accounts: &[AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 验证指令账户数量
        if instruction.accounts.len() > accounts.len() {
            msg!("账户数量不足: 需要 {}, 提供 {}", instruction.accounts.len(), accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 映射账户到指令
        let mut account_infos = Vec::with_capacity(instruction.accounts.len());
        for i in 0..instruction.accounts.len() {
            if i < accounts.len() {
                account_infos.push(accounts[i].clone());
            } else {
                return Err(RouteError::InvalidAccountCount.into());
            }
        }

        // 记录CPI调用详情
        msg!("执行Meteora DLMM CPI: 程序 {}, 账户数 {}",
            instruction.program_id, account_infos.len());

        // 执行CPI调用
        match owner_seeds {
            Some(seeds) => {
                invoke_signed(instruction, &account_infos, seeds)
                    .map_err(|e| {
                        msg!("Meteora DLMM CPI调用失败 (带签名): {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
            None => {
                invoke(instruction, &account_infos)
                    .map_err(|e| {
                        msg!("Meteora DLMM CPI调用失败: {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
        }

        msg!("Meteora DLMM CPI调用成功");
        Ok(())
    }

    /// 创建DLMM交换参数
    pub fn create_dlmm_swap_params(
        lb_pair: Pubkey,
        reserve_x: Pubkey,
        reserve_y: Pubkey,
        token_x_mint: Pubkey,
        token_y_mint: Pubkey,
        oracle: Pubkey,
        active_id: i32,
        bin_step: u16,
        swap_for_y: bool,
        host_fee_recipient: Option<Pubkey>,
    ) -> DlmmSwapParams {
        DlmmSwapParams {
            lb_pair,
            reserve_x,
            reserve_y,
            token_x_mint,
            token_y_mint,
            oracle,
            active_id,
            bin_step,
            swap_for_y,
            host_fee_recipient,
        }
    }
}

/// Meteora DAMM适配器
///
/// 实现Dynamic AMM的交换逻辑
/// 支持动态费率调整、推荐人费用分成和价格发现
pub struct MeteoraAmmProcessor {
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 价格影响阈值（基点）
    pub max_price_impact_bps: u16,
}

impl Default for MeteoraAmmProcessor {
    fn default() -> Self {
        Self {
            max_slippage_bps: 100, // 1%
            max_price_impact_bps: 300, // 3%
        }
    }
}

/// DAMM交换参数
#[derive(BorshDeserialize, BorshSerialize, Debug, Clone)]
pub struct DammSwapParams {
    pub pool: Pubkey,
    pub token_a_vault: Pubkey,
    pub token_b_vault: Pubkey,
    pub token_a_mint: Pubkey,
    pub token_b_mint: Pubkey,
    pub swap_a_to_b: bool,
    pub referral: Option<Pubkey>,
}

impl DexProcessor for MeteoraAmmProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行Meteora DAMM交换: {} -> {}, 最小输出: {}", amount_in, min_amount_out, min_amount_out);

        // 解析DAMM交换参数
        let swap_params = self.parse_damm_swap_params(&route.swap_data)?;

        // 构建池数据结构
        let pool_data = MeteoraDAMMPool {
            pool: swap_params.pool,
            token_a_vault: swap_params.token_a_vault,
            token_b_vault: swap_params.token_b_vault,
            token_a_mint: swap_params.token_a_mint,
            token_b_mint: swap_params.token_b_mint,
        };

        // 预估输出并验证滑点
        let estimated_output = self.estimate_damm_output(
            &pool_data,
            amount_in,
            swap_params.swap_a_to_b,
        )?;

        if estimated_output < min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 构建交换指令
        let user = accounts[8].key(); // 用户在第8个位置（基于账户布局）
        let swap_instruction = MeteoraDAMMSwapInstruction::build_swap_instruction(
            &user,
            amount_in,
            min_amount_out,
            swap_params.swap_a_to_b,
            swap_params.referral,
        ).map_err(|e| {
            msg!("构建Meteora DAMM指令失败: {:?}", e);
            RouteError::DexOperationFailed
        })?;

        // 执行CPI调用
        self.execute_damm_cpi(&swap_instruction, accounts, owner_seeds)?;

        Ok(estimated_output)
    }

    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()> {
        // 验证账户数量（13-14个账户，取决于是否有推荐人）
        if accounts.len() < 13 {
            msg!("Meteora DAMM需要至少13个账户，实际: {}", accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 解析交换参数
        let swap_params = self.parse_damm_swap_params(&route.swap_data)?;

        // 验证关键账户
        let indices = self.get_account_indices(accounts)?;

        // 验证用户签名权限
        if hop_index == 0 && !accounts[indices.swap_authority].is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        // 验证源代币账户
        let source_token_account = &accounts[indices.source_token_account];
        let source_token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if source_token_data.mint != route.input_mint {
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证目标代币账户
        let dest_token_account = &accounts[indices.destination_token_account];
        let dest_token_data = TokenAccount::try_deserialize(
            &mut dest_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if dest_token_data.mint != route.output_mint {
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证池账户
        if let Some(pool_idx) = indices.pool_account {
            if *accounts[pool_idx].key != swap_params.pool {
                return Err(RouteError::InvalidDexAccounts.into());
            }
        }

        msg!("Meteora DAMM账户验证通过: {} -> {}", route.input_mint, route.output_mint);
        Ok(())
    }

    fn get_account_indices(
        &self,
        accounts: &[AccountInfo],
    ) -> Result<SwapAccountIndices> {
        // Meteora DAMM的标准账户布局
        // 0: pool_authority
        // 1: pool
        // 2: input_token_account
        // 3: output_token_account
        // 4: token_a_vault
        // 5: token_b_vault
        // 6: token_a_mint
        // 7: token_b_mint
        // 8: payer (signer)
        // 9: token_a_program
        // 10: token_b_program
        // 11: referral_token_account (可选)
        // 12: event_authority
        // 13: program

        if accounts.len() < 13 {
            return Err(RouteError::InvalidAccountCount.into());
        }

        let has_referral = accounts.len() >= 14;
        let mut additional_accounts = vec![0, 4, 5, 6, 7, 9, 10];

        if has_referral {
            additional_accounts.push(11);
            additional_accounts.extend_from_slice(&[12, 13]);
        } else {
            additional_accounts.extend_from_slice(&[11, 12]);
        }

        Ok(SwapAccountIndices {
            source_token_account: 2,
            destination_token_account: 3,
            swap_authority: 8,
            dex_program: if has_referral { 13 } else { 12 },
            pool_account: Some(1),
            additional_accounts,
        })
    }

    fn dex_name(&self) -> &'static str {
        "Meteora DAMM"
    }

    fn dex_type(&self) -> Dex {
        Dex::MeteoraAmm
    }

    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // DAMM相对简单
        120_000
    }
}

impl MeteoraAmmProcessor {
    /// 解析DAMM交换参数
    fn parse_damm_swap_params(&self, swap_data: &[u8]) -> Result<DammSwapParams> {
        if swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        DammSwapParams::try_from_slice(swap_data)
            .map_err(|e| {
                msg!("解析DAMM交换参数失败: {:?}", e);
                RouteError::InvalidRouteConfig.into()
            })
    }

    /// 预估DAMM交换输出
    fn estimate_damm_output(
        &self,
        _pool_data: &MeteoraDAMMPool,
        amount_in: u64,
        swap_a_to_b: bool,
    ) -> Result<u64> {
        // 动态费率（DAMM支持动态费率调整）
        let dynamic_fee_bps = self.calculate_dynamic_amm_fee(amount_in)?;

        let fee_amount = amount_in
            .checked_mul(dynamic_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10_000)
            .ok_or(RouteError::DivisionByZero)?;

        let amount_after_fee = amount_in
            .checked_sub(fee_amount)
            .ok_or(RouteError::MathUnderflow)?;

        // 简化的恒定乘积计算（实际应该从池状态读取储备量）
        // DAMM可能有额外的价格发现机制
        let estimated_output = if swap_a_to_b {
            amount_after_fee
        } else {
            amount_after_fee
        };

        msg!("预估DAMM输出: 输入 {} -> 输出 {}, 动态费用 {}",
            amount_in, estimated_output, fee_amount);

        Ok(estimated_output)
    }

    /// 计算动态AMM费率
    fn calculate_dynamic_amm_fee(&self, amount_in: u64) -> Result<u16> {
        // 基于交换数量的动态费率
        let fee_bps = if amount_in > 1_000_000_000 { // > 1000 tokens
            50  // 0.5% for large trades
        } else if amount_in > 100_000_000 { // > 100 tokens
            30  // 0.3% for medium trades
        } else {
            20  // 0.2% for small trades
        };

        Ok(fee_bps)
    }

    /// 执行DAMM CPI调用
    fn execute_damm_cpi<'info>(
        &self,
        instruction: &Instruction,
        accounts: &[AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 验证指令账户数量
        if instruction.accounts.len() > accounts.len() {
            msg!("账户数量不足: 需要 {}, 提供 {}", instruction.accounts.len(), accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 映射账户到指令
        let mut account_infos = Vec::with_capacity(instruction.accounts.len());
        for i in 0..instruction.accounts.len() {
            if i < accounts.len() {
                account_infos.push(accounts[i].clone());
            } else {
                return Err(RouteError::InvalidAccountCount.into());
            }
        }

        // 记录CPI调用详情
        msg!("执行Meteora DAMM CPI: 程序 {}, 账户数 {}",
            instruction.program_id, account_infos.len());

        // 执行CPI调用
        match owner_seeds {
            Some(seeds) => {
                invoke_signed(instruction, &account_infos, seeds)
                    .map_err(|e| {
                        msg!("Meteora DAMM CPI调用失败 (带签名): {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
            None => {
                invoke(instruction, &account_infos)
                    .map_err(|e| {
                        msg!("Meteora DAMM CPI调用失败: {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
        }

        msg!("Meteora DAMM CPI调用成功");
        Ok(())
    }

    /// 创建DAMM交换参数
    pub fn create_damm_swap_params(
        pool: Pubkey,
        token_a_vault: Pubkey,
        token_b_vault: Pubkey,
        token_a_mint: Pubkey,
        token_b_mint: Pubkey,
        swap_a_to_b: bool,
        referral: Option<Pubkey>,
    ) -> DammSwapParams {
        DammSwapParams {
            pool,
            token_a_vault,
            token_b_vault,
            token_a_mint,
            token_b_mint,
            swap_a_to_b,
            referral,
        }
    }
}

/// Meteora工具函数
pub mod utils {
    use super::*;

    /// 计算bin价格从active_id
    pub fn calculate_bin_price(active_id: i32, bin_step: u16) -> Result<f64> {
        if bin_step == 0 {
            return Err(RouteError::DivisionByZero.into());
        }

        let bin_step_factor = 1.0 + (bin_step as f64 / 10000.0);
        let price = bin_step_factor.powi(active_id);

        Ok(price)
    }

    /// 计算DLMM的最优bin范围
    pub fn calculate_optimal_bin_range(
        active_id: i32,
        _amount_in: u64,
        max_bins: usize,
    ) -> Result<Vec<i32>> {
        let mut bin_ids = Vec::with_capacity(max_bins);

        // 从active_id开始，向两边扩展
        bin_ids.push(active_id);

        for i in 1..max_bins / 2 {
            if bin_ids.len() >= max_bins {
                break;
            }

            // 添加更高的bin
            bin_ids.push(active_id + i as i32);

            if bin_ids.len() >= max_bins {
                break;
            }

            // 添加更低的bin
            bin_ids.push(active_id - i as i32);
        }

        bin_ids.sort();
        Ok(bin_ids)
    }

    /// 验证Meteora池地址格式
    pub fn validate_meteora_pool_address(pool: &Pubkey) -> Result<()> {
        if pool == &Pubkey::default() {
            return Err(RouteError::InvalidRouteConfig.into());
        }
        Ok(())
    }

    /// 计算推荐人费用
    pub fn calculate_referral_fee(amount: u64, referral_rate_bps: u16) -> Result<u64> {
        amount
            .checked_mul(referral_rate_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero.into())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dlmm_processor_creation() {
        let processor = MeteoraLbProcessor::default();
        assert_eq!(processor.max_slippage_bps, 150);
        assert_eq!(processor.max_price_impact_bps, 500);
        assert_eq!(processor.dex_name(), "Meteora DLMM");
        assert_eq!(processor.dex_type(), Dex::MeteoraLb);
    }

    #[test]
    fn test_damm_processor_creation() {
        let processor = MeteoraAmmProcessor::default();
        assert_eq!(processor.max_slippage_bps, 100);
        assert_eq!(processor.max_price_impact_bps, 300);
        assert_eq!(processor.dex_name(), "Meteora DAMM");
        assert_eq!(processor.dex_type(), Dex::MeteoraAmm);
    }

    #[test]
    fn test_dlmm_swap_params_serialization() {
        let params = DlmmSwapParams {
            lb_pair: Pubkey::new_unique(),
            reserve_x: Pubkey::new_unique(),
            reserve_y: Pubkey::new_unique(),
            token_x_mint: Pubkey::new_unique(),
            token_y_mint: Pubkey::new_unique(),
            oracle: Pubkey::new_unique(),
            active_id: 8388608, // 示例active_id
            bin_step: 25,
            swap_for_y: true,
            host_fee_recipient: None,
        };

        let serialized = params.try_to_vec().unwrap();
        let deserialized = DlmmSwapParams::try_from_slice(&serialized).unwrap();

        assert_eq!(params.lb_pair, deserialized.lb_pair);
        assert_eq!(params.active_id, deserialized.active_id);
        assert_eq!(params.swap_for_y, deserialized.swap_for_y);
    }

    #[test]
    fn test_damm_swap_params_serialization() {
        let params = DammSwapParams {
            pool: Pubkey::new_unique(),
            token_a_vault: Pubkey::new_unique(),
            token_b_vault: Pubkey::new_unique(),
            token_a_mint: Pubkey::new_unique(),
            token_b_mint: Pubkey::new_unique(),
            swap_a_to_b: true,
            referral: Some(Pubkey::new_unique()),
        };

        let serialized = params.try_to_vec().unwrap();
        let deserialized = DammSwapParams::try_from_slice(&serialized).unwrap();

        assert_eq!(params.pool, deserialized.pool);
        assert_eq!(params.swap_a_to_b, deserialized.swap_a_to_b);
        assert!(deserialized.referral.is_some());
    }

    #[test]
    fn test_dynamic_fee_calculation() {
        let processor = MeteoraLbProcessor::default();

        // 测试不同bin_step的费率
        assert_eq!(processor.calculate_dynamic_fee(5).unwrap(), 100);  // 1%
        assert_eq!(processor.calculate_dynamic_fee(25).unwrap(), 50);  // 0.5%
        assert_eq!(processor.calculate_dynamic_fee(50).unwrap(), 25);  // 0.25%
        assert_eq!(processor.calculate_dynamic_fee(100).unwrap(), 10); // 0.1%
        assert_eq!(processor.calculate_dynamic_fee(200).unwrap(), 5);  // 0.05%
    }

    #[test]
    fn test_bin_price_calculation() {
        // 测试bin价格计算
        let price = utils::calculate_bin_price(0, 25).unwrap();
        assert!((price - 1.0).abs() < 0.0001); // active_id=0时价格应该是1

        let price_positive = utils::calculate_bin_price(100, 25).unwrap();
        assert!(price_positive > 1.0); // 正的active_id应该有更高的价格

        let price_negative = utils::calculate_bin_price(-100, 25).unwrap();
        assert!(price_negative < 1.0); // 负的active_id应该有更低的价格
    }

    #[test]
    fn test_optimal_bin_range() {
        let bin_range = utils::calculate_optimal_bin_range(8388608, 1000000, 10).unwrap();

        assert_eq!(bin_range.len(), 10);
        assert!(bin_range.contains(&8388608)); // 应该包含active_id
        assert!(bin_range.is_sorted()); // 应该是排序的
    }

    #[test]
    fn test_utils_functions() {
        // 测试地址验证
        assert!(utils::validate_meteora_pool_address(&Pubkey::default()).is_err());
        assert!(utils::validate_meteora_pool_address(&Pubkey::new_unique()).is_ok());

        // 测试推荐人费用计算
        let referral_fee = utils::calculate_referral_fee(10000, 100).unwrap(); // 1%
        assert_eq!(referral_fee, 100);
    }
}
