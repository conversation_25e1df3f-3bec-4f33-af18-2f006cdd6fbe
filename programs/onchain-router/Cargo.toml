[workspace]
resolver = "2"
members = [
    "programs/onchain-router"
]

[workspace.package]
name = "onchain-router"
version = "0.1.0"
edition = "2021"

[profile.release]
overflow-checks = true

[workspace.dependencies]
# Anchor framework
anchor-lang = { version = "0.31.1", features = ["init-if-needed", "event-cpi"]}
anchor-spl = "0.31.1"

# Solana dependencies
solana-program = "2.2.1"
spl-token = "6.0.0"
spl-associated-token-account = "4.0.0"

# Serialization
borsh = "1.5.1"


